import { VxeUI } from 'vxe-pc-ui'

// 模拟查询接口
const findCustomSetting = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(id)
      try {
        if (localStorage.getItem(id)) {
          resolve(JSON.parse(localStorage.getItem(id) || ''))
        } else {
          resolve({})
        }
      } catch (e) {
        resolve({})
      }
    }, 300)
  })
}

// 模拟保存接口
const saveCustomSetting = (id, storeData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(storeData)
      localStorage.setItem(id, JSON.stringify(storeData))
      VxeUI.modal.message({
        status: 'success',
        content: '保存个性化列设置成功'
      })
      resolve({})
    }, 200)
  })
}

export const vxeCustomConfig: any = reactive({
  storage: true, // 启用自定义列状态保存功能
  restoreStore({ id }) {
    // 从服务端调用接口获取当前用户表格自定义列数据，支持异步，返回 Promise
    return findCustomSetting(id)
  },
  updateStore({ id, storeData }) {
    // 当 storage 启用后，默认会自动保存在浏览器本地 localStorage 里面，可以通过自定义改方法，使用服务端保存
    // 将用户自定义的列数据保存到服务端，支持异步，返回 Promise
    return saveCustomSetting(id, storeData)
  },
  checkMethod({ column }) {
    // 禁用动态列
    return !column.field?.startsWith('dynamic_')
  }
})

/** 单元格点击高亮行列 */
export const tableCellClick = ({ row, column }: any, tableRef: any) => {
  const $table = tableRef
  if ($table) {
    $table.setCurrentRow(row)
    $table.setCurrentColumn(column)
  }
}



/**
 * 创建通用的过滤处理函数
 * @param queryParams 查询参数对象的引用
 * @param handleQuery 查询回调函数
 * @param config 配置选项
 * @returns 处理过滤变化的函数
 */
export function createFilterHandler(
  queryParams: Record<string, any>,
  handleQuery: () => void,
  config: FilterConfig = {}
) {
  const { specialFields = [], reservedFields = ['pageNo', 'pageSize', 'sorting', 'hasUnComplete'] } = config

  return function handleFilterChange(params: { filterList: FilterItem[] }) {
    // 初始化 filters 对象
    const filters: Record<string, any> = {}

    params.filterList.forEach((item: FilterItem) => {
      const { field, values, datas } = item
      if (specialFields.includes(field) && values.length > 0) {
        // 特定字段且 values 有值
        filters[field] = values
      } else if (datas.length > 0) {
        // 其他字段且 datas 有值
        filters[field] = datas[0]
      }
    })

    // 清空不在 params.filterList 中的字段
    Object.keys(queryParams).forEach((key) => {
      if (
        !reservedFields.includes(key) &&
        !params.filterList.some((item: FilterItem) => item.field === key)
      ) {
        queryParams[key] = undefined
      }
    })
    
    // 更新 queryParams
    Object.assign(queryParams, filters)

    // 调用查询接口
    handleQuery()
  }
}