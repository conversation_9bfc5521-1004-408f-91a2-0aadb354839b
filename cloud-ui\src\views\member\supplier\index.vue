<template>
  <!-- 列表 -->
  <ContentWrap>
    <vxe-toolbar ref="toolbarRef" custom size="mini">
      <template #buttons>
        <el-button type="success" size="small" @click="handleQuery" plain> 刷新数据 </el-button>
        <el-button type="primary" size="small" @click="openForm('create')">添加会员</el-button>
      </template>
    </vxe-toolbar>
    <div class="h-[calc(100vh-200px)]">
      <BasicsTable
        ref="tableRef"
        id="member-user-table"
        :loading="loading"
        :data="list"
        height="100%"
        :filter-config="{ remote: true }"
        @filter-change="handleFilterChange"
      >
        <vxe-column title="营业执照" />
      </BasicsTable>
    </div>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="getList"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
const queryParams = ref<any>({
  pageSize: 20,
  pageNo: 1
})

const total = ref(0)
const list = ref<any[]>([])
const loading = ref(false)

async function getList(){
  
}
</script>
